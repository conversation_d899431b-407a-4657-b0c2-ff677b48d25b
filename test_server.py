#!/usr/bin/env python3
"""
Teste simples do servidor para verificar se as informações de endereço são exibidas corretamente.
"""

import gradio as gr

def create_simple_interface():
    """Cria uma interface simples para teste."""
    with gr.<PERSON><PERSON>(title="Teste Servidor") as demo:
        gr.Markdown("# 🧪 Teste do Servidor")
        gr.Markdown("Interface de teste para verificar exibição de endereços.")
        
        # Definir configurações do servidor
        server_host = "0.0.0.0"
        server_port = 7862  # Usar porta diferente para teste
        
        # Função para encontrar uma porta disponível
        def find_available_port(start_port, max_attempts=10):
            import socket
            for port in range(start_port, start_port + max_attempts):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(('localhost', port))
                        return port
                except OSError:
                    continue
            return None
        
        # Tentar encontrar uma porta disponível
        available_port = find_available_port(server_port)
        if available_port and available_port != server_port:
            print(f"⚠️  Porta {server_port} em uso, usando porta {available_port}")
            server_port = available_port
        
        # Mostrar informações do servidor antes de iniciar
        print("\n" + "="*70)
        print("🧪 TESTE - INICIANDO SERVIDOR")
        print("="*70)
        print(f"🏠 Host: {server_host}")
        print(f"🔌 Porta: {server_port}")
        print(f"🌍 URL Local: http://localhost:{server_port}")
        print(f"🌐 URL da Rede: http://{server_host}:{server_port}")
        print("="*70)
        print("📝 Para acessar o servidor:")
        print(f"   • No mesmo computador: http://localhost:{server_port}")
        print(f"   • De outros dispositivos na rede: http://[SEU_IP]:{server_port}")
        print("="*70)
        print("⚠️  Para parar o servidor, pressione Ctrl+C")
        print("="*70)
        
        # Iniciar o servidor Gradio
        print("\n🌐 Iniciando interface web...")
        try:
            launch_info = demo.launch(
                server_name=server_host, 
                server_port=server_port, 
                show_error=True, 
                prevent_thread_lock=True,
                quiet=False
            )
            
            # Exibir informações adicionais após o lançamento
            print("\n" + "="*70)
            print("✅ SERVIDOR INICIADO COM SUCESSO!")
            print("="*70)
            
            if hasattr(launch_info, 'local_url'):
                print(f"🌐 Interface disponível em: {launch_info.local_url}")
                if getattr(launch_info, 'share_url', None):
                    print(f"🔗 Link público: {launch_info.share_url}")
            elif isinstance(launch_info, str):
                print(f"🌐 Interface disponível em: {launch_info}")
            else:
                print(f"🌐 Interface disponível em: http://localhost:{server_port}")
            
            print("="*70)
            print("🎯 O servidor está rodando e pronto para uso!")
            print("📱 Abra o link acima no seu navegador para acessar a interface")
            print("="*70)
            
        except Exception as e:
            print(f"\n❌ Erro ao iniciar servidor: {e}")
            print("💡 Dicas para resolver:")
            print(f"   • Verifique se a porta {server_port} não está em uso")
            print("   • Tente usar uma porta diferente")
            print("   • Verifique as permissões de rede")
            print("   • Execute: lsof -i :{server_port} para ver o que está usando a porta")

if __name__ == "__main__":
    print("🚀 Iniciando teste do servidor...")
    create_simple_interface()
